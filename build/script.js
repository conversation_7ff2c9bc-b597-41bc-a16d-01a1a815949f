/**
 * EW Simple Slider - Frontend JavaScript
 * Vanilla JavaScript implementation with accessibility and touch support
 */

(function() {
	'use strict';

	/**
	 * EWSimpleSlider class
	 */
	class EWSimpleSlider {
		constructor(element) {
			this.slider = element;
			this.container = element.querySelector('.ew-simple-slider__container');
			this.slidesContainer = element.querySelector('.ew-simple-slider__slides');
			this.slides = Array.from(element.querySelectorAll('.ew-simple-slider__slide'));
			this.prevButton = element.querySelector('.ew-simple-slider__arrow--prev');
			this.nextButton = element.querySelector('.ew-simple-slider__arrow--next');
			this.dots = Array.from(element.querySelectorAll('.ew-simple-slider__dot'));
			this.playPauseButton = element.querySelector('.ew-simple-slider__play-pause');
			this.progressBar = element.querySelector('.ew-simple-slider__progress-bar');
			this.srAnnouncements = element.querySelector('.ew-simple-slider__sr-announcements');

			// Settings from data attributes
			this.settings = {
				autoplay: element.dataset.autoplay === 'true',
				autoplaySpeed: parseInt(element.dataset.autoplaySpeed) || 5000,
				transitionSpeed: parseInt(element.dataset.transitionSpeed) || 300,
				animationType: element.dataset.animationType || 'slide',
				pauseOnHover: element.dataset.pauseOnHover === 'true',
				infinite: element.dataset.infinite === 'true',
				slidesToShow: {
					mobile: parseInt(element.dataset.slidesToShowMobile) || 1,
					tablet: parseInt(element.dataset.slidesToShowTablet) || 2,
					desktop: parseInt(element.dataset.slidesToShowDesktop) || 3
				}
			};

			// State
			this.currentSlide = 0;
			this.isPlaying = this.settings.autoplay;
			this.autoplayTimer = null;
			this.progressTimer = null;
			this.isTransitioning = false;
			this.touchStartX = 0;
			this.touchEndX = 0;
			this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

			// Initialize
			this.init();
		}

		/**
		 * Initialize the slider
		 */
		init() {
			if (this.slides.length === 0) return;

			// Respect reduced motion preference
			if (this.prefersReducedMotion) {
				this.settings.autoplay = false;
				this.isPlaying = false;
			}

			// Set up initial state
			this.updateSlideVisibility();
			this.updateDots();
			this.updateArrows();

			// Bind events
			this.bindEvents();

			// Start autoplay if enabled
			if (this.settings.autoplay && this.isPlaying) {
				this.startAutoplay();
			}

			// Update play/pause button state
			this.updatePlayPauseButton();

			// Announce initial slide to screen readers
			this.announceSlide();
		}

		/**
		 * Bind event listeners
		 */
		bindEvents() {
			// Arrow navigation
			if (this.prevButton) {
				this.prevButton.addEventListener('click', () => this.prevSlide());
			}
			if (this.nextButton) {
				this.nextButton.addEventListener('click', () => this.nextSlide());
			}

			// Dot navigation
			this.dots.forEach((dot, index) => {
				dot.addEventListener('click', () => this.goToSlide(index));
			});

			// Play/pause button
			if (this.playPauseButton) {
				this.playPauseButton.addEventListener('click', () => this.togglePlayPause());
			}

			// Keyboard navigation
			this.container.addEventListener('keydown', (e) => this.handleKeydown(e));

			// Touch events
			this.container.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
			this.container.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });

			// Mouse events for pause on hover
			if (this.settings.pauseOnHover && this.settings.autoplay) {
				this.container.addEventListener('mouseenter', () => this.pauseAutoplay());
				this.container.addEventListener('mouseleave', () => {
					if (this.isPlaying) {
						this.startAutoplay();
					}
				});
			}

			// Focus events for accessibility
			this.container.addEventListener('focusin', () => this.pauseAutoplay());
			this.container.addEventListener('focusout', () => {
				if (this.isPlaying && !this.container.contains(document.activeElement)) {
					this.startAutoplay();
				}
			});

			// Visibility change (pause when tab is not active)
			document.addEventListener('visibilitychange', () => {
				if (document.hidden) {
					this.pauseAutoplay();
				} else if (this.isPlaying) {
					this.startAutoplay();
				}
			});

			// Resize handler for responsive behavior
			window.addEventListener('resize', this.debounce(() => {
				this.updateSlideVisibility();
			}, 250));
		}

		/**
		 * Handle keyboard navigation
		 */
		handleKeydown(event) {
			switch (event.key) {
				case 'ArrowLeft':
					event.preventDefault();
					this.prevSlide();
					break;
				case 'ArrowRight':
					event.preventDefault();
					this.nextSlide();
					break;
				case 'Home':
					event.preventDefault();
					this.goToSlide(0);
					break;
				case 'End':
					event.preventDefault();
					this.goToSlide(this.slides.length - 1);
					break;
				case ' ':
				case 'Enter':
					if (event.target === this.playPauseButton) {
						event.preventDefault();
						this.togglePlayPause();
					}
					break;
			}
		}

		/**
		 * Handle touch start
		 */
		handleTouchStart(event) {
			this.touchStartX = event.changedTouches[0].screenX;
		}

		/**
		 * Handle touch end
		 */
		handleTouchEnd(event) {
			this.touchEndX = event.changedTouches[0].screenX;
			this.handleSwipe();
		}

		/**
		 * Handle swipe gesture
		 */
		handleSwipe() {
			const swipeThreshold = 50;
			const diff = this.touchStartX - this.touchEndX;

			if (Math.abs(diff) > swipeThreshold) {
				if (diff > 0) {
					// Swipe left - next slide
					this.nextSlide();
				} else {
					// Swipe right - previous slide
					this.prevSlide();
				}
			}
		}

		/**
		 * Go to previous slide
		 */
		prevSlide() {
			if (this.isTransitioning) return;

			let newIndex;
			if (this.currentSlide === 0) {
				newIndex = this.settings.infinite ? this.slides.length - 1 : 0;
			} else {
				newIndex = this.currentSlide - 1;
			}

			if (newIndex !== this.currentSlide) {
				this.goToSlide(newIndex);
			}
		}

		/**
		 * Go to next slide
		 */
		nextSlide() {
			if (this.isTransitioning) return;

			let newIndex;
			if (this.currentSlide === this.slides.length - 1) {
				newIndex = this.settings.infinite ? 0 : this.slides.length - 1;
			} else {
				newIndex = this.currentSlide + 1;
			}

			if (newIndex !== this.currentSlide) {
				this.goToSlide(newIndex);
			}
		}

		/**
		 * Go to specific slide
		 */
		goToSlide(index) {
			if (this.isTransitioning || index === this.currentSlide || index < 0 || index >= this.slides.length) {
				return;
			}

			this.isTransitioning = true;
			const previousSlide = this.currentSlide;
			this.currentSlide = index;

			// Update slide visibility
			this.updateSlideVisibility();
			this.updateDots();
			this.updateArrows();

			// Announce slide change to screen readers
			this.announceSlide();

			// Reset autoplay progress
			if (this.isPlaying) {
				this.resetAutoplayProgress();
			}

			// Allow transitions again after animation completes
			setTimeout(() => {
				this.isTransitioning = false;
			}, this.settings.transitionSpeed);
		}

		/**
		 * Update slide visibility and active states
		 */
		updateSlideVisibility() {
			this.slides.forEach((slide, index) => {
				const isActive = index === this.currentSlide;
				slide.classList.toggle('is-active', isActive);
				slide.setAttribute('aria-hidden', isActive ? 'false' : 'true');

				if (isActive) {
					slide.removeAttribute('tabindex');
				} else {
					slide.setAttribute('tabindex', '-1');
				}

				// Lazy load images for upcoming slides
				this.lazyLoadSlideImages(slide, index);
			});

			// Update slides container transform for slide animation
			if (this.settings.animationType === 'slide') {
				const translateX = -this.currentSlide * 100;
				this.slidesContainer.style.transform = `translateX(${translateX}%)`;
			}
		}

		/**
		 * Lazy load images for slides
		 */
		lazyLoadSlideImages(slide, slideIndex) {
			const img = slide.querySelector('.ew-simple-slider__slide-image');
			const container = slide.querySelector('.ew-simple-slider__image-container');
			if (!img || !container) return;

			// Load current slide and next/previous slides
			const shouldLoad = Math.abs(slideIndex - this.currentSlide) <= 1;

			if (shouldLoad && !img.complete && !container.classList.contains('loading')) {
				container.classList.add('loading');

				// Handle image load event
				const handleLoad = () => {
					container.classList.remove('loading');
					container.classList.add('loaded');
					img.removeEventListener('load', handleLoad);
					img.removeEventListener('error', handleError);
				};

				const handleError = () => {
					container.classList.remove('loading');
					container.classList.add('error');
					img.removeEventListener('load', handleLoad);
					img.removeEventListener('error', handleError);
				};

				img.addEventListener('load', handleLoad);
				img.addEventListener('error', handleError);

				// Force load if not already loading
				if (img.loading === 'lazy') {
					img.loading = 'eager';
				}
			}
		}

		/**
		 * Update dot navigation
		 */
		updateDots() {
			this.dots.forEach((dot, index) => {
				const isActive = index === this.currentSlide;
				dot.classList.toggle('is-active', isActive);
				dot.setAttribute('aria-selected', isActive ? 'true' : 'false');
				dot.setAttribute('tabindex', isActive ? '0' : '-1');
			});
		}

		/**
		 * Update arrow buttons
		 */
		updateArrows() {
			if (!this.settings.infinite) {
				if (this.prevButton) {
					this.prevButton.disabled = this.currentSlide === 0;
				}
				if (this.nextButton) {
					this.nextButton.disabled = this.currentSlide === this.slides.length - 1;
				}
			}
		}

		/**
		 * Start autoplay
		 */
		startAutoplay() {
			if (!this.settings.autoplay || this.slides.length <= 1) return;

			this.clearAutoplay();
			this.autoplayTimer = setTimeout(() => {
				this.nextSlide();
				this.startAutoplay();
			}, this.settings.autoplaySpeed);

			// Start progress bar animation
			if (this.progressBar) {
				this.progressBar.style.transitionDuration = `${this.settings.autoplaySpeed}ms`;
				this.progressBar.style.width = '100%';
			}
		}

		/**
		 * Pause autoplay
		 */
		pauseAutoplay() {
			this.clearAutoplay();
			if (this.progressBar) {
				this.progressBar.style.transitionDuration = '0ms';
				this.progressBar.style.width = '0%';
			}
		}

		/**
		 * Clear autoplay timers
		 */
		clearAutoplay() {
			if (this.autoplayTimer) {
				clearTimeout(this.autoplayTimer);
				this.autoplayTimer = null;
			}
		}

		/**
		 * Reset autoplay progress
		 */
		resetAutoplayProgress() {
			if (this.progressBar) {
				this.progressBar.style.transitionDuration = '0ms';
				this.progressBar.style.width = '0%';
				
				// Force reflow
				this.progressBar.offsetHeight;
				
				// Restart progress
				if (this.isPlaying) {
					this.progressBar.style.transitionDuration = `${this.settings.autoplaySpeed}ms`;
					this.progressBar.style.width = '100%';
				}
			}
		}

		/**
		 * Toggle play/pause
		 */
		togglePlayPause() {
			if (this.isPlaying) {
				this.isPlaying = false;
				this.pauseAutoplay();
			} else {
				this.isPlaying = true;
				this.startAutoplay();
			}
			this.updatePlayPauseButton();
		}

		/**
		 * Update play/pause button state
		 */
		updatePlayPauseButton() {
			if (!this.playPauseButton) return;

			const state = this.isPlaying ? 'playing' : 'paused';
			this.playPauseButton.setAttribute('data-state', state);
			
			const label = this.isPlaying 
				? ewSliderData.strings.pauseSlider 
				: ewSliderData.strings.playSlider;
			this.playPauseButton.setAttribute('aria-label', label);
		}

		/**
		 * Announce slide change to screen readers
		 */
		announceSlide() {
			if (!this.srAnnouncements) return;

			const announcement = ewSliderData.strings.slideOf
				.replace('%1$d', this.currentSlide + 1)
				.replace('%2$d', this.slides.length);
			
			this.srAnnouncements.textContent = announcement;
		}

		/**
		 * Debounce utility function
		 */
		debounce(func, wait) {
			let timeout;
			return function executedFunction(...args) {
				const later = () => {
					clearTimeout(timeout);
					func(...args);
				};
				clearTimeout(timeout);
				timeout = setTimeout(later, wait);
			};
		}

		/**
		 * Destroy the slider instance
		 */
		destroy() {
			this.clearAutoplay();
			// Remove event listeners would go here if needed
		}
	}

	/**
	 * Initialize all sliders on the page
	 */
	function initSliders() {
		const sliders = document.querySelectorAll('.ew-simple-slider');
		sliders.forEach(slider => {
			if (!slider.ewSliderInstance) {
				slider.ewSliderInstance = new EWSimpleSlider(slider);
			}
		});
	}

	/**
	 * Initialize when DOM is ready
	 */
	if (document.readyState === 'loading') {
		document.addEventListener('DOMContentLoaded', initSliders);
	} else {
		initSliders();
	}

	// Re-initialize on dynamic content changes (for AJAX-loaded content)
	if (typeof MutationObserver !== 'undefined') {
		const observer = new MutationObserver((mutations) => {
			mutations.forEach((mutation) => {
				if (mutation.type === 'childList') {
					mutation.addedNodes.forEach((node) => {
						if (node.nodeType === 1) { // Element node
							const newSliders = node.querySelectorAll ? 
								node.querySelectorAll('.ew-simple-slider') : [];
							newSliders.forEach(slider => {
								if (!slider.ewSliderInstance) {
									slider.ewSliderInstance = new EWSimpleSlider(slider);
								}
							});
						}
					});
				}
			});
		});

		observer.observe(document.body, {
			childList: true,
			subtree: true
		});
	}

})();
