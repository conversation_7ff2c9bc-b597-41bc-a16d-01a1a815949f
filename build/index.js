(()=>{"use strict";var e,l={293:()=>{const e=window.wp.blocks,l=window.wp.i18n,i=window.React,t=window.wp.blockEditor,s=window.wp.components,a=window.wp.element,r=window.wp.data,n=window.wp.primitives,d=(0,i.createElement)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(n.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})),o=(0,i.createElement)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(n.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})),m=(0,i.createElement)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(n.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})),p=(0,i.createElement)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(n.Path,{d:"m19 7-3-3-8.5 8.5-1 4 4-1L19 7Zm-7 11.5H5V20h7v-1.5Z"})),c=(0,i.createElement)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,i.createElement)(n.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"}));function w({slide:e,index:t,isActive:a,onEdit:r,onRemove:n}){return(0,i.createElement)("div",{className:"ew-simple-slider__slide-preview"},(0,i.createElement)("div",{className:"ew-simple-slider__slide-image"},(0,i.createElement)("img",{src:e.url,alt:e.alt||"",loading:"lazy"}),(0,i.createElement)("div",{className:"ew-simple-slider__slide-overlay"},(0,i.createElement)("div",{className:"ew-simple-slider__slide-actions"},(0,i.createElement)(s.Button,{icon:p,size:"small",variant:"secondary",onClick:r,label:(0,l.__)("Edit slide","ew-simple-slider")}),(0,i.createElement)(s.Button,{icon:c,size:"small",variant:"secondary",onClick:n,label:(0,l.__)("Remove slide","ew-simple-slider"),isDestructive:!0})))),(e.title||e.description)&&(0,i.createElement)("div",{className:"ew-simple-slider__slide-content"},e.title&&(0,i.createElement)("h3",{className:"ew-simple-slider__slide-title"},e.title),e.description&&(0,i.createElement)("p",{className:"ew-simple-slider__slide-description"},e.description)),(0,i.createElement)("div",{className:"ew-simple-slider__slide-number"},t+1))}function _({slide:e,onUpdate:t,onClose:r}){const[n,d]=(0,a.useState)({title:e.title||"",description:e.description||"",link:e.link||"",linkTarget:e.linkTarget||"_self",alt:e.alt||""}),o=(e,l)=>{d(i=>({...i,[e]:l}))},m=()=>{r()};return(0,i.createElement)(s.Modal,{title:(0,l.__)("Edit Slide","ew-simple-slider"),onRequestClose:m,className:"ew-simple-slider__slide-editor-modal",size:"medium"},(0,i.createElement)("div",{className:"ew-simple-slider__slide-editor"},(0,i.createElement)(s.Card,{className:"ew-simple-slider__slide-editor-preview"},(0,i.createElement)(s.CardHeader,null,(0,i.createElement)("h4",null,(0,l.__)("Preview","ew-simple-slider"))),(0,i.createElement)(s.CardBody,null,(0,i.createElement)("div",{className:"ew-simple-slider__slide-editor-image"},(0,i.createElement)("img",{src:e.url,alt:n.alt||e.alt,style:{width:"100%",height:"auto"}}),(n.title||n.description)&&(0,i.createElement)("div",{className:"ew-simple-slider__slide-editor-overlay"},n.title&&(0,i.createElement)("h3",{className:"ew-simple-slider__slide-title"},n.title),n.description&&(0,i.createElement)("p",{className:"ew-simple-slider__slide-description"},n.description))))),(0,i.createElement)(s.Card,{className:"ew-simple-slider__slide-editor-form"},(0,i.createElement)(s.CardHeader,null,(0,i.createElement)("h4",null,(0,l.__)("Slide Settings","ew-simple-slider"))),(0,i.createElement)(s.CardBody,null,(0,i.createElement)("div",{className:"ew-simple-slider__form-fields"},(0,i.createElement)(s.TextControl,{label:(0,l.__)("Alt Text","ew-simple-slider"),value:n.alt,onChange:e=>o("alt",e),help:(0,l.__)("Describe the image for screen readers and SEO","ew-simple-slider")}),(0,i.createElement)(s.TextControl,{label:(0,l.__)("Title","ew-simple-slider"),value:n.title,onChange:e=>o("title",e),help:(0,l.__)("Optional title displayed over the image","ew-simple-slider"),placeholder:(0,l.__)("Enter slide title...","ew-simple-slider")}),(0,i.createElement)(s.TextareaControl,{label:(0,l.__)("Description","ew-simple-slider"),value:n.description,onChange:e=>o("description",e),help:(0,l.__)("Optional description displayed under the title","ew-simple-slider"),placeholder:(0,l.__)("Enter slide description...","ew-simple-slider"),rows:3}),(0,i.createElement)(s.TextControl,{label:(0,l.__)("Link URL","ew-simple-slider"),value:n.link,onChange:e=>o("link",e),help:(0,l.__)("Optional link when slide is clicked","ew-simple-slider"),placeholder:(0,l.__)("https://example.com","ew-simple-slider"),type:"url"}),n.link&&(0,i.createElement)(s.SelectControl,{label:(0,l.__)("Link Target","ew-simple-slider"),value:n.linkTarget,options:[{label:(0,l.__)("Same window","ew-simple-slider"),value:"_self"},{label:(0,l.__)("New window","ew-simple-slider"),value:"_blank"}],onChange:e=>o("linkTarget",e)})))),(0,i.createElement)(s.Flex,{className:"ew-simple-slider__slide-editor-actions",justify:"flex-end"},(0,i.createElement)(s.FlexItem,null,(0,i.createElement)(s.Button,{variant:"tertiary",onClick:m},(0,l.__)("Cancel","ew-simple-slider"))),(0,i.createElement)(s.FlexItem,null,(0,i.createElement)(s.Button,{variant:"primary",onClick:()=>{t(n),r()}},(0,l.__)("Save Changes","ew-simple-slider"))))))}const u=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"ew-simple-slider/slider","version":"1.0.0","title":"EW Simple Slider","category":"media","icon":"slides","description":"Un slider minimalista y accesible con diseño responsive y funcionalidades esenciales.","keywords":["slider","carousel","gallery","images","responsive"],"textdomain":"ew-simple-slider","supports":{"html":false,"align":["wide","full"],"spacing":{"margin":true,"padding":true},"color":{"background":true,"text":true,"gradients":true,"__experimentalDefaultControls":{"background":true,"text":true}},"typography":{"fontSize":true,"lineHeight":true}},"attributes":{"slides":{"type":"array","default":[],"items":{"type":"object","properties":{"id":{"type":"number"},"url":{"type":"string"},"alt":{"type":"string","default":""},"title":{"type":"string","default":""},"description":{"type":"string","default":""},"link":{"type":"string","default":""},"linkTarget":{"type":"string","default":"_self"}}}},"autoplay":{"type":"boolean","default":false},"autoplaySpeed":{"type":"number","default":5000},"transitionSpeed":{"type":"number","default":300},"animationType":{"type":"string","default":"slide","enum":["slide","fade"]},"showArrows":{"type":"boolean","default":true},"showDots":{"type":"boolean","default":true},"pauseOnHover":{"type":"boolean","default":true},"infinite":{"type":"boolean","default":true},"slidesToShow":{"type":"object","default":{"mobile":1,"tablet":2,"desktop":3}},"aspectRatio":{"type":"string","default":"16/9"},"imageSize":{"type":"string","default":"large"},"arrowStyle":{"type":"string","default":"default","enum":["default","minimal","rounded"]},"dotStyle":{"type":"string","default":"default","enum":["default","minimal","square"]}},"providesContext":{"ew-simple-slider/slides":"slides","ew-simple-slider/settings":{"autoplay":"autoplay","autoplaySpeed":"autoplaySpeed","transitionSpeed":"transitionSpeed","animationType":"animationType"}},"example":{"attributes":{"slides":[{"id":1,"url":"https://via.placeholder.com/800x450/007cba/ffffff?text=Slide+1","alt":"Slide de ejemplo 1","title":"Primer Slide","description":"Descripción del primer slide de ejemplo"},{"id":2,"url":"https://via.placeholder.com/800x450/28a745/ffffff?text=Slide+2","alt":"Slide de ejemplo 2","title":"Segundo Slide","description":"Descripción del segundo slide de ejemplo"},{"id":3,"url":"https://via.placeholder.com/800x450/dc3545/ffffff?text=Slide+3","alt":"Slide de ejemplo 3","title":"Tercer Slide","description":"Descripción del tercer slide de ejemplo"}],"autoplay":true,"showArrows":true,"showDots":true}},"editorScript":"file:./build/index.js","editorStyle":"file:./build/index.css","style":"file:./build/style-index.css","script":"file:./build/script.js"}');(0,e.registerBlockType)(u.name,{...u,title:(0,l.__)("EW Simple Slider","ew-simple-slider"),description:(0,l.__)("Un slider minimalista y accesible con diseño responsive y funcionalidades esenciales.","ew-simple-slider"),keywords:[(0,l.__)("slider","ew-simple-slider"),(0,l.__)("carousel","ew-simple-slider"),(0,l.__)("gallery","ew-simple-slider"),(0,l.__)("images","ew-simple-slider"),(0,l.__)("responsive","ew-simple-slider")],edit:function({attributes:e,setAttributes:n,clientId:u}){const{slides:h,autoplay:g,autoplaySpeed:v,transitionSpeed:E,animationType:y,showArrows:b,showDots:f,pauseOnHover:S,infinite:k,slidesToShow:N,aspectRatio:x,imageSize:C,arrowStyle:T,dotStyle:B}=e,[A,R]=(0,a.useState)(0),[O,D]=(0,a.useState)(null),{imageSizes:H}=(0,r.useSelect)(e=>({imageSizes:e("core/editor")?.getEditorSettings()?.imageSizes||[]})),z=(0,t.useBlockProps)({className:`ew-simple-slider ew-simple-slider--${y} ew-simple-slider--editing`}),P=e=>{const l={id:e.id,url:e.url,alt:e.alt||"",title:"",description:"",link:"",linkTarget:"_self"};n({slides:[...h,l]})},V=e=>{const l=h.filter((l,i)=>i!==e);n({slides:l}),A>=l.length&&l.length>0?R(l.length-1):0===l.length&&R(0)},j=e=>{R(e)};return(0,a.useEffect)(()=>{h.length>0&&A>=h.length&&R(h.length-1)},[h.length,A]),(0,i.createElement)("div",{...z},(0,i.createElement)(t.BlockControls,null,(0,i.createElement)(s.ToolbarGroup,null,(0,i.createElement)(t.MediaUploadCheck,null,(0,i.createElement)(t.MediaUpload,{onSelect:P,allowedTypes:["image"],multiple:!1,render:({open:e})=>(0,i.createElement)(s.ToolbarButton,{icon:d,label:(0,l.__)("Add slide","ew-simple-slider"),onClick:e})})))),(0,i.createElement)(t.InspectorControls,null,(0,i.createElement)(s.PanelBody,{title:(0,l.__)("Slider Settings","ew-simple-slider"),initialOpen:!0},(0,i.createElement)(s.ToggleControl,{label:(0,l.__)("Autoplay","ew-simple-slider"),checked:g,onChange:e=>n({autoplay:e}),help:(0,l.__)("Automatically advance slides","ew-simple-slider")}),g&&(0,i.createElement)(s.RangeControl,{label:(0,l.__)("Autoplay Speed (ms)","ew-simple-slider"),value:v,onChange:e=>n({autoplaySpeed:e}),min:1e3,max:1e4,step:500}),(0,i.createElement)(s.RangeControl,{label:(0,l.__)("Transition Speed (ms)","ew-simple-slider"),value:E,onChange:e=>n({transitionSpeed:e}),min:100,max:1e3,step:50}),(0,i.createElement)(s.SelectControl,{label:(0,l.__)("Animation Type","ew-simple-slider"),value:y,options:[{label:(0,l.__)("Slide","ew-simple-slider"),value:"slide"},{label:(0,l.__)("Fade","ew-simple-slider"),value:"fade"}],onChange:e=>n({animationType:e})}),(0,i.createElement)(s.SelectControl,{label:(0,l.__)("Aspect Ratio","ew-simple-slider"),value:x,options:[{label:"16:9",value:"16/9"},{label:"21:9",value:"21/9"},{label:"4:3",value:"4/3"},{label:"3:2",value:"3/2"},{label:"1:1",value:"1/1"}],onChange:e=>n({aspectRatio:e})})),(0,i.createElement)(s.PanelBody,{title:(0,l.__)("Navigation","ew-simple-slider"),initialOpen:!1},(0,i.createElement)(s.ToggleControl,{label:(0,l.__)("Show Arrows","ew-simple-slider"),checked:b,onChange:e=>n({showArrows:e})}),b&&(0,i.createElement)(s.SelectControl,{label:(0,l.__)("Arrow Style","ew-simple-slider"),value:T,options:[{label:(0,l.__)("Default","ew-simple-slider"),value:"default"},{label:(0,l.__)("Minimal","ew-simple-slider"),value:"minimal"},{label:(0,l.__)("Rounded","ew-simple-slider"),value:"rounded"}],onChange:e=>n({arrowStyle:e})}),(0,i.createElement)(s.ToggleControl,{label:(0,l.__)("Show Dots","ew-simple-slider"),checked:f,onChange:e=>n({showDots:e})}),f&&(0,i.createElement)(s.SelectControl,{label:(0,l.__)("Dot Style","ew-simple-slider"),value:B,options:[{label:(0,l.__)("Default","ew-simple-slider"),value:"default"},{label:(0,l.__)("Minimal","ew-simple-slider"),value:"minimal"},{label:(0,l.__)("Square","ew-simple-slider"),value:"square"}],onChange:e=>n({dotStyle:e})}),(0,i.createElement)(s.ToggleControl,{label:(0,l.__)("Pause on Hover","ew-simple-slider"),checked:S,onChange:e=>n({pauseOnHover:e})}),(0,i.createElement)(s.ToggleControl,{label:(0,l.__)("Infinite Loop","ew-simple-slider"),checked:k,onChange:e=>n({infinite:e})}))),(0,i.createElement)("div",{className:"ew-simple-slider__editor"},0===h.length?(0,i.createElement)("div",{className:"ew-simple-slider__empty-state"},(0,i.createElement)("div",{className:"ew-simple-slider__empty-content"},(0,i.createElement)(s.Icon,{icon:"slides",size:48}),(0,i.createElement)("h3",null,(0,l.__)("EW Simple Slider","ew-simple-slider")),(0,i.createElement)("p",null,(0,l.__)("Add images to create your slider","ew-simple-slider")),(0,i.createElement)(t.MediaUploadCheck,null,(0,i.createElement)(t.MediaUpload,{onSelect:P,allowedTypes:["image"],multiple:!1,render:({open:e})=>(0,i.createElement)(s.Button,{variant:"primary",onClick:e},(0,l.__)("Add Images","ew-simple-slider"))})))):(0,i.createElement)("div",{className:"ew-simple-slider__preview"},(0,i.createElement)("div",{className:"ew-simple-slider__container",style:{aspectRatio:x}},(0,i.createElement)("div",{className:"ew-simple-slider__slides"},h.map((e,l)=>(0,i.createElement)("div",{key:e.id||l,className:"ew-simple-slider__slide "+(l===A?"is-active":""),style:{transform:`translateX(${100*(l-A)}%)`}},(0,i.createElement)(w,{slide:e,index:l,isActive:l===A,onEdit:()=>D(l),onRemove:()=>V(l)})))),b&&h.length>1&&(0,i.createElement)(i.Fragment,null,(0,i.createElement)("button",{className:`ew-simple-slider__arrow ew-simple-slider__arrow--prev ew-simple-slider__arrow--${T}`,onClick:()=>{0!==h.length&&R(e=>0===e?h.length-1:e-1)},"aria-label":(0,l.__)("Previous slide","ew-simple-slider")},(0,i.createElement)(s.Icon,{icon:o})),(0,i.createElement)("button",{className:`ew-simple-slider__arrow ew-simple-slider__arrow--next ew-simple-slider__arrow--${T}`,onClick:()=>{0!==h.length&&R(e=>e===h.length-1?0:e+1)},"aria-label":(0,l.__)("Next slide","ew-simple-slider")},(0,i.createElement)(s.Icon,{icon:m}))),f&&h.length>1&&(0,i.createElement)("div",{className:`ew-simple-slider__dots ew-simple-slider__dots--${B}`},h.map((e,t)=>(0,i.createElement)("button",{key:t,className:"ew-simple-slider__dot "+(t===A?"is-active":""),onClick:()=>j(t),"aria-label":(0,l.__)("Go to slide %d","ew-simple-slider").replace("%d",t+1)})))),(0,i.createElement)("div",{className:"ew-simple-slider__slide-manager"},(0,i.createElement)("div",{className:"ew-simple-slider__slide-thumbnails"},h.map((e,t)=>(0,i.createElement)("div",{key:e.id||t,className:"ew-simple-slider__thumbnail "+(t===A?"is-active":""),onClick:()=>j(t)},(0,i.createElement)("img",{src:e.url,alt:e.alt}),(0,i.createElement)("div",{className:"ew-simple-slider__thumbnail-actions"},(0,i.createElement)(s.Button,{icon:p,size:"small",onClick:()=>D(t),label:(0,l.__)("Edit slide","ew-simple-slider")}),(0,i.createElement)(s.Button,{icon:c,size:"small",onClick:()=>V(t),label:(0,l.__)("Remove slide","ew-simple-slider"),isDestructive:!0})))),(0,i.createElement)(t.MediaUploadCheck,null,(0,i.createElement)(t.MediaUpload,{onSelect:P,allowedTypes:["image"],multiple:!1,render:({open:e})=>(0,i.createElement)("div",{className:"ew-simple-slider__thumbnail ew-simple-slider__add-slide",onClick:e},(0,i.createElement)(s.Icon,{icon:d,size:24}),(0,i.createElement)("span",null,(0,l.__)("Add Slide","ew-simple-slider")))}))))),null!==O&&(0,i.createElement)(_,{slide:h[O],onUpdate:e=>((e,l)=>{const i=[...h];i[e]={...i[e],...l},n({slides:i})})(O,e),onClose:()=>D(null)})))},save:function({attributes:e}){const{slides:s,autoplay:a,autoplaySpeed:r,transitionSpeed:n,animationType:d,showArrows:o,showDots:m,pauseOnHover:p,infinite:c,slidesToShow:w,aspectRatio:_,arrowStyle:u,dotStyle:h}=e;if(!s||0===s.length)return null;const g=t.useBlockProps.save({className:`ew-simple-slider ew-simple-slider--${d}`,"data-autoplay":a,"data-autoplay-speed":r,"data-transition-speed":n,"data-animation-type":d,"data-pause-on-hover":p,"data-infinite":c,"data-slides-to-show-mobile":w.mobile,"data-slides-to-show-tablet":w.tablet,"data-slides-to-show-desktop":w.desktop}),v="ew-simple-slider__container "+(g.className?.includes("has-")&&g.className?.includes("-background-color")?"has-background":"");return(0,i.createElement)("div",{...g},(0,i.createElement)("div",{className:v,style:{aspectRatio:_},role:"region","aria-label":(0,l.__)("Image slider","ew-simple-slider")},(0,i.createElement)("div",{className:"ew-simple-slider__slides",role:"group","aria-live":"polite","aria-atomic":"false"},s.map((e,t)=>{const a=(0,i.createElement)("div",{className:"ew-simple-slider__slide-content"},(0,i.createElement)("div",{className:"ew-simple-slider__image-container"},(0,i.createElement)("div",{className:"ew-simple-slider__image-placeholder"},(0,i.createElement)("div",{className:"ew-simple-slider__loading-spinner"})),(0,i.createElement)("img",{src:e.url,alt:e.alt||"",className:"ew-simple-slider__slide-image",loading:"lazy",decoding:"async",fetchpriority:0===t?"high":"low"})),(e.title||e.description)&&(0,i.createElement)("div",{className:"ew-simple-slider__slide-overlay"},(0,i.createElement)("div",{className:"ew-simple-slider__slide-text"},e.title&&(0,i.createElement)("h3",{className:"ew-simple-slider__slide-title"},e.title),e.description&&(0,i.createElement)("p",{className:"ew-simple-slider__slide-description"},e.description))));return(0,i.createElement)("div",{key:e.id||t,id:`slide-${t}`,className:"ew-simple-slider__slide "+(0===t?"is-active":""),role:"group","aria-roledescription":(0,l.__)("slide","ew-simple-slider"),"aria-label":(0,l.__)("Slide %1$d of %2$d","ew-simple-slider").replace("%1$d",t+1).replace("%2$d",s.length),"data-slide-index":t,"aria-hidden":0===t?"false":"true"},e.link?(0,i.createElement)("a",{href:e.link,target:e.linkTarget||"_self",rel:"_blank"===e.linkTarget?"noopener noreferrer":void 0,className:"ew-simple-slider__slide-link","aria-label":e.title||e.alt||(0,l.__)("View slide content","ew-simple-slider")},a):a)})),o&&s.length>1&&(0,i.createElement)("div",{className:"ew-simple-slider__navigation"},(0,i.createElement)("button",{className:`ew-simple-slider__arrow ew-simple-slider__arrow--prev ew-simple-slider__arrow--${u}`,type:"button","aria-label":(0,l.__)("Previous slide","ew-simple-slider"),"data-direction":"prev"},(0,i.createElement)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true"},(0,i.createElement)("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})),(0,i.createElement)("span",{className:"screen-reader-text"},(0,l.__)("Previous slide","ew-simple-slider"))),(0,i.createElement)("button",{className:`ew-simple-slider__arrow ew-simple-slider__arrow--next ew-simple-slider__arrow--${u}`,type:"button","aria-label":(0,l.__)("Next slide","ew-simple-slider"),"data-direction":"next"},(0,i.createElement)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true"},(0,i.createElement)("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})),(0,i.createElement)("span",{className:"screen-reader-text"},(0,l.__)("Next slide","ew-simple-slider")))),m&&s.length>1&&(0,i.createElement)("div",{className:`ew-simple-slider__dots ew-simple-slider__dots--${h}`,role:"tablist","aria-label":(0,l.__)("Slide navigation","ew-simple-slider")},s.map((e,t)=>(0,i.createElement)("button",{key:t,className:"ew-simple-slider__dot "+(0===t?"is-active":""),type:"button",role:"tab","aria-selected":0===t?"true":"false","aria-controls":`slide-${t}`,"aria-label":(0,l.__)("Go to slide %d","ew-simple-slider").replace("%d",t+1),"data-slide-index":t,tabIndex:0===t?0:-1},(0,i.createElement)("span",{className:"screen-reader-text"},(0,l.__)("Slide %d","ew-simple-slider").replace("%d",t+1))))),a&&(0,i.createElement)("div",{className:"ew-simple-slider__autoplay-controls"},(0,i.createElement)("button",{className:"ew-simple-slider__play-pause",type:"button","aria-label":(0,l.__)("Pause slideshow","ew-simple-slider"),"data-action":"pause"},(0,i.createElement)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",className:"ew-simple-slider__pause-icon"},(0,i.createElement)("rect",{x:"6",y:"4",width:"4",height:"16",fill:"currentColor"}),(0,i.createElement)("rect",{x:"14",y:"4",width:"4",height:"16",fill:"currentColor"})),(0,i.createElement)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",className:"ew-simple-slider__play-icon",style:{display:"none"}},(0,i.createElement)("polygon",{points:"5,3 19,12 5,21",fill:"currentColor"})),(0,i.createElement)("span",{className:"screen-reader-text"},(0,l.__)("Pause slideshow","ew-simple-slider")))),(0,i.createElement)("div",{className:"ew-simple-slider__sr-announcements","aria-live":"polite","aria-atomic":"true",style:{position:"absolute",left:"-10000px",width:"1px",height:"1px",overflow:"hidden"}}),a&&(0,i.createElement)("div",{className:"ew-simple-slider__progress"},(0,i.createElement)("div",{className:"ew-simple-slider__progress-bar"}))))}}),(0,e.registerBlockVariation)(u.name,{name:"hero-slider",title:(0,l.__)("Hero Slider","ew-simple-slider"),description:(0,l.__)("Slider optimizado para secciones hero con imágenes grandes","ew-simple-slider"),icon:"cover-image",attributes:{aspectRatio:"21/9",slidesToShow:{mobile:1,tablet:1,desktop:1},autoplay:!0,showDots:!1,arrowStyle:"minimal"},scope:["inserter"]}),(0,e.registerBlockVariation)(u.name,{name:"gallery-slider",title:(0,l.__)("Gallery Slider","ew-simple-slider"),description:(0,l.__)("Slider para mostrar múltiples imágenes como galería","ew-simple-slider"),icon:"format-gallery",attributes:{aspectRatio:"4/3",slidesToShow:{mobile:1,tablet:2,desktop:3},autoplay:!1,showDots:!0,arrowStyle:"default"},scope:["inserter"]}),(0,e.registerBlockVariation)(u.name,{name:"testimonial-slider",title:(0,l.__)("Testimonial Slider","ew-simple-slider"),description:(0,l.__)("Slider diseñado para mostrar testimonios con texto","ew-simple-slider"),icon:"format-quote",attributes:{aspectRatio:"16/9",slidesToShow:{mobile:1,tablet:1,desktop:1},autoplay:!0,autoplaySpeed:8e3,showArrows:!1,showDots:!0,dotStyle:"minimal"},scope:["inserter"]})}},i={};function t(e){var s=i[e];if(void 0!==s)return s.exports;var a=i[e]={exports:{}};return l[e](a,a.exports,t),a.exports}t.m=l,e=[],t.O=(l,i,s,a)=>{if(!i){var r=1/0;for(m=0;m<e.length;m++){for(var[i,s,a]=e[m],n=!0,d=0;d<i.length;d++)(!1&a||r>=a)&&Object.keys(t.O).every(e=>t.O[e](i[d]))?i.splice(d--,1):(n=!1,a<r&&(r=a));if(n){e.splice(m--,1);var o=s();void 0!==o&&(l=o)}}return l}a=a||0;for(var m=e.length;m>0&&e[m-1][2]>a;m--)e[m]=e[m-1];e[m]=[i,s,a]},t.o=(e,l)=>Object.prototype.hasOwnProperty.call(e,l),(()=>{var e={57:0,350:0};t.O.j=l=>0===e[l];var l=(l,i)=>{var s,a,[r,n,d]=i,o=0;if(r.some(l=>0!==e[l])){for(s in n)t.o(n,s)&&(t.m[s]=n[s]);if(d)var m=d(t)}for(l&&l(i);o<r.length;o++)a=r[o],t.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return t.O(m)},i=globalThis.webpackChunkew_simple_slider=globalThis.webpackChunkew_simple_slider||[];i.forEach(l.bind(null,0)),i.push=l.bind(null,i.push.bind(i))})();var s=t.O(void 0,[350],()=>t(293));s=t.O(s)})();