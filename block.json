{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "ew-simple-slider/slider", "version": "1.0.0", "title": "EW Simple Slider", "category": "media", "icon": "slides", "description": "Un slider minimalista y accesible con diseño responsive y funcionalidades esenciales.", "keywords": ["slider", "carousel", "gallery", "images", "responsive"], "textdomain": "ew-simple-slider", "supports": {"html": false, "align": ["wide", "full"], "spacing": {"margin": true, "padding": true}, "color": {"background": true, "text": true, "gradients": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "typography": {"fontSize": true, "lineHeight": true}}, "attributes": {"slides": {"type": "array", "default": [], "items": {"type": "object", "properties": {"id": {"type": "number"}, "url": {"type": "string"}, "alt": {"type": "string", "default": ""}, "title": {"type": "string", "default": ""}, "description": {"type": "string", "default": ""}, "link": {"type": "string", "default": ""}, "linkTarget": {"type": "string", "default": "_self"}}}}, "autoplay": {"type": "boolean", "default": false}, "autoplaySpeed": {"type": "number", "default": 5000}, "transitionSpeed": {"type": "number", "default": 300}, "animationType": {"type": "string", "default": "slide", "enum": ["slide", "fade"]}, "showArrows": {"type": "boolean", "default": true}, "showDots": {"type": "boolean", "default": true}, "pauseOnHover": {"type": "boolean", "default": true}, "infinite": {"type": "boolean", "default": true}, "slidesToShow": {"type": "object", "default": {"mobile": 1, "tablet": 2, "desktop": 3}}, "aspectRatio": {"type": "string", "default": "16/9"}, "imageSize": {"type": "string", "default": "large"}, "arrowStyle": {"type": "string", "default": "default", "enum": ["default", "minimal", "rounded"]}, "dotStyle": {"type": "string", "default": "default", "enum": ["default", "minimal", "square"]}}, "providesContext": {"ew-simple-slider/slides": "slides", "ew-simple-slider/settings": {"autoplay": "autoplay", "autoplaySpeed": "autoplaySpeed", "transitionSpeed": "transitionSpeed", "animationType": "animationType"}}, "example": {"attributes": {"slides": [{"id": 1, "url": "https://via.placeholder.com/800x450/007cba/ffffff?text=Slide+1", "alt": "Slide de ejemplo 1", "title": "Primer Slide", "description": "Descripción del primer slide de ejemplo"}, {"id": 2, "url": "https://via.placeholder.com/800x450/28a745/ffffff?text=Slide+2", "alt": "Slide de ejemplo 2", "title": "<PERSON><PERSON><PERSON>", "description": "Descripción del segundo slide de ejemplo"}, {"id": 3, "url": "https://via.placeholder.com/800x450/dc3545/ffffff?text=Slide+3", "alt": "Slide de ejemplo 3", "title": "Tercer Slide", "description": "Descripción del tercer slide de ejemplo"}], "autoplay": true, "showArrows": true, "showDots": true}}, "editorScript": "file:./build/index.js", "editorStyle": "file:./build/index.css", "style": "file:./build/style-index.css", "script": "file:./build/script.js"}