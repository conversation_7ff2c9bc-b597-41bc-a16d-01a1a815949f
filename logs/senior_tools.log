2025-08-22 04:36:58,373 - INFO - setup_logging:45 - Logger configurado correctamente - Archivo: logs/senior_tools.log
2025-08-22 04:36:58,373 - INFO - <module>:52 - ================================================================================
2025-08-22 04:36:58,373 - INFO - <module>:53 - MÓDULO SENIOR_TOOLS INICIALIZADO
2025-08-22 04:36:58,374 - INFO - <module>:54 - ================================================================================
2025-08-22 04:36:58,374 - DEBUG - __init__:153 - Initializing server 'senior_tools'
2025-08-22 04:36:58,374 - DEBUG - decorator:385 - Registering handler for ListToolsRequest
2025-08-22 04:36:58,374 - DEBUG - decorator:262 - Registering handler for ListResourcesRequest
2025-08-22 04:36:58,374 - DEBUG - decorator:275 - Registering handler for ListResourceTemplatesRequest
2025-08-22 04:36:58,374 - DEBUG - decorator:234 - Registering handler for PromptListRequest
2025-08-22 04:36:58,375 - DEBUG - decorator:446 - Registering handler for CallToolRequest
2025-08-22 04:36:58,375 - DEBUG - decorator:290 - Registering handler for ReadResourceRequest
2025-08-22 04:36:58,375 - DEBUG - decorator:249 - Registering handler for GetPromptRequest
2025-08-22 04:36:58,380 - INFO - <module>:57 - FastMCP instance created successfully
2025-08-22 04:36:58,386 - INFO - <module>:1170 - INICIANDO SERVIDOR MCP - senior_tools
2025-08-22 04:36:58,386 - INFO - <module>:1171 - Ejecutando mcp.run()...
2025-08-22 04:36:58,420 - DEBUG - __init__:64 - Using selector: EpollSelector
2025-08-22 04:36:58,465 - DEBUG - run:587 - Received message: root=InitializedNotification(method='notifications/initialized', params=None, jsonrpc='2.0')
2025-08-22 04:36:58,466 - DEBUG - run:587 - Received message: <mcp.shared.session.RequestResponder object at 0x79ffda61a8d0>
2025-08-22 04:36:58,467 - INFO - _handle_request:624 - Processing request of type ListToolsRequest
2025-08-22 04:36:58,467 - DEBUG - _handle_request:626 - Dispatching request of type ListToolsRequest
2025-08-22 04:36:58,468 - DEBUG - _handle_request:673 - Response sent
