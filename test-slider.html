<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EW Simple Slider - Test</title>
    <link rel="stylesheet" href="build/style-index.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 40px;
        }
        .test-section h2 {
            color: #007cba;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>EW Simple Slider - Prueba de Funcionamiento</h1>
        
        <div class="test-section">
            <h2>Slider Básico</h2>
            <div class="ew-simple-slider" 
                 data-autoplay="true" 
                 data-autoplay-speed="5000" 
                 data-transition-speed="300" 
                 data-animation-type="slide"
                 data-pause-on-hover="true"
                 data-infinite="true"
                 data-slides-to-show-mobile="1"
                 data-slides-to-show-tablet="1"
                 data-slides-to-show-desktop="1">
                <div class="ew-simple-slider__container" style="aspect-ratio: 16/9">
                    <div class="ew-simple-slider__slides">
                        <div id="slide-0" class="ew-simple-slider__slide is-active" data-slide-index="0" aria-hidden="false">
                            <div class="ew-simple-slider__slide-content">
                                <img src="https://via.placeholder.com/800x450/007cba/ffffff?text=Slide+1"
                                     alt="Slide 1"
                                     class="ew-simple-slider__slide-image"
                                     loading="eager"
                                     decoding="async"
                                     fetchpriority="high">
                                <div class="ew-simple-slider__slide-overlay">
                                    <div class="ew-simple-slider__slide-text">
                                        <h3 class="ew-simple-slider__slide-title">Primer Slide</h3>
                                        <p class="ew-simple-slider__slide-description">Esta es la descripción del primer slide de prueba.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="slide-1" class="ew-simple-slider__slide" data-slide-index="1" aria-hidden="true">
                            <div class="ew-simple-slider__slide-content">
                                <img src="https://via.placeholder.com/800x450/28a745/ffffff?text=Slide+2"
                                     alt="Slide 2"
                                     class="ew-simple-slider__slide-image"
                                     loading="lazy"
                                     decoding="async"
                                     fetchpriority="low">
                                <div class="ew-simple-slider__slide-overlay">
                                    <div class="ew-simple-slider__slide-text">
                                        <h3 class="ew-simple-slider__slide-title">Segundo Slide</h3>
                                        <p class="ew-simple-slider__slide-description">Esta es la descripción del segundo slide de prueba.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="slide-2" class="ew-simple-slider__slide" data-slide-index="2" aria-hidden="true">
                            <div class="ew-simple-slider__slide-content">
                                <img src="https://via.placeholder.com/800x450/dc3545/ffffff?text=Slide+3"
                                     alt="Slide 3"
                                     class="ew-simple-slider__slide-image"
                                     loading="lazy"
                                     decoding="async"
                                     fetchpriority="low">
                                <div class="ew-simple-slider__slide-overlay">
                                    <div class="ew-simple-slider__slide-text">
                                        <h3 class="ew-simple-slider__slide-title">Tercer Slide</h3>
                                        <p class="ew-simple-slider__slide-description">Esta es la descripción del tercer slide de prueba.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Navigation arrows -->
                    <div class="ew-simple-slider__navigation">
                        <button class="ew-simple-slider__arrow ew-simple-slider__arrow--prev ew-simple-slider__arrow--default" 
                                type="button" 
                                aria-label="Previous slide" 
                                data-direction="prev">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                        <button class="ew-simple-slider__arrow ew-simple-slider__arrow--next ew-simple-slider__arrow--default" 
                                type="button" 
                                aria-label="Next slide" 
                                data-direction="next">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- Dots navigation -->
                    <div class="ew-simple-slider__dots ew-simple-slider__dots--default" role="tablist" aria-label="Slide navigation">
                        <button class="ew-simple-slider__dot is-active" type="button" role="tab" aria-selected="true" aria-controls="slide-0" aria-label="Go to slide 1" data-slide-index="0" tabindex="0">
                            <span class="screen-reader-text">Slide 1</span>
                        </button>
                        <button class="ew-simple-slider__dot" type="button" role="tab" aria-selected="false" aria-controls="slide-1" aria-label="Go to slide 2" data-slide-index="1" tabindex="-1">
                            <span class="screen-reader-text">Slide 2</span>
                        </button>
                        <button class="ew-simple-slider__dot" type="button" role="tab" aria-selected="false" aria-controls="slide-2" aria-label="Go to slide 3" data-slide-index="2" tabindex="-1">
                            <span class="screen-reader-text">Slide 3</span>
                        </button>
                    </div>
                    
                    <!-- Autoplay controls -->
                    <div class="ew-simple-slider__autoplay-controls">
                        <button class="ew-simple-slider__play-pause" type="button" data-state="playing">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ew-simple-slider__pause-icon">
                                <rect x="6" y="4" width="4" height="16" fill="currentColor"/>
                                <rect x="14" y="4" width="4" height="16" fill="currentColor"/>
                            </svg>
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ew-simple-slider__play-icon" style="display: none;">
                                <polygon points="5,3 19,12 5,21" fill="currentColor"/>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- Progress indicator -->
                    <div class="ew-simple-slider__progress">
                        <div class="ew-simple-slider__progress-bar"></div>
                    </div>
                    
                    <!-- Screen reader announcements -->
                    <div class="ew-simple-slider__sr-announcements" aria-live="polite" aria-atomic="true" style="position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;"></div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Información del Plugin</h2>
            <ul>
                <li><strong>Nombre:</strong> EW Simple Slider</li>
                <li><strong>Versión:</strong> 1.0.0</li>
                <li><strong>Autor:</strong> Luis Eduardo G. González</li>
                <li><strong>Website:</strong> <a href="https://especialistaenwp.com/optimizador-pro">especialistaenwp.com/optimizador-pro</a></li>
                <li><strong>Características:</strong> Minimalista, Accesible, Responsive, Touch-friendly</li>
            </ul>
        </div>
    </div>

    <script>
        // Mock del objeto ewSliderData para la prueba
        window.ewSliderData = {
            strings: {
                prevSlide: 'Slide anterior',
                nextSlide: 'Siguiente slide',
                pauseSlider: 'Pausar slider',
                playSlider: 'Reproducir slider',
                slideOf: 'Slide %1$d de %2$d'
            }
        };
    </script>
    <script src="build/script.js"></script>
</body>
</html>
