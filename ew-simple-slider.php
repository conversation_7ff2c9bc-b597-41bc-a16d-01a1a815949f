<?php
/**
 * Plugin Name: EW Simple Slider
 * Plugin URI: https://especialistaenwp.com/optimizador-pro
 * Description: Un slider minimalista y accesible para <PERSON><PERSON><PERSON> con diseño responsive y funcionalidades esenciales.
 * Version: 1.0.0
 * Author: <PERSON>
 * Author URI: https://especialistaenwp.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: ew-simple-slider
 * Domain Path: /languages
 * Requires at least: 5.8
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package EWSimpleSlider
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('EW_SIMPLE_SLIDER_VERSION', '1.0.0');
define('EW_SIMPLE_SLIDER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('EW_SIMPLE_SLIDER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('EW_SIMPLE_SLIDER_PLUGIN_FILE', __FILE__);

/**
 * Main plugin class
 */
class EW_Simple_Slider {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('enqueue_block_editor_assets', array($this, 'enqueue_editor_assets'));
        
        // Load text domain
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Register the block
        $this->register_block();
    }
    
    /**
     * Register the slider block
     */
    public function register_block() {
        // Register block from block.json in root directory
        $result = register_block_type(EW_SIMPLE_SLIDER_PLUGIN_DIR . 'block.json');

        // Debug: Log registration result
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('EW Simple Slider block registration result: ' . ($result ? 'SUCCESS' : 'FAILED'));
            error_log('Block.json path: ' . EW_SIMPLE_SLIDER_PLUGIN_DIR . 'block.json');
        }
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Only enqueue if block is present on the page
        if (has_block('ew-simple-slider/slider')) {
            wp_enqueue_style(
                'ew-simple-slider-style',
                EW_SIMPLE_SLIDER_PLUGIN_URL . 'build/style-index.css',
                array(),
                EW_SIMPLE_SLIDER_VERSION
            );
            
            wp_enqueue_script(
                'ew-simple-slider-script',
                EW_SIMPLE_SLIDER_PLUGIN_URL . 'build/script.js',
                array(),
                EW_SIMPLE_SLIDER_VERSION,
                true
            );
            
            // Localize script for AJAX and translations
            wp_localize_script('ew-simple-slider-script', 'ewSliderData', array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('ew_slider_nonce'),
                'strings' => array(
                    'prevSlide' => __('Previous slide', 'ew-simple-slider'),
                    'nextSlide' => __('Next slide', 'ew-simple-slider'),
                    'pauseSlider' => __('Pause slider', 'ew-simple-slider'),
                    'playSlider' => __('Play slider', 'ew-simple-slider'),
                    'slideOf' => __('Slide %1$d of %2$d', 'ew-simple-slider'),
                )
            ));
        }
    }
    
    /**
     * Enqueue editor assets
     */
    public function enqueue_editor_assets() {
        wp_enqueue_style(
            'ew-simple-slider-editor',
            EW_SIMPLE_SLIDER_PLUGIN_URL . 'build/index.css',
            array('wp-edit-blocks'),
            EW_SIMPLE_SLIDER_VERSION
        );
    }
    
    /**
     * Load plugin text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'ew-simple-slider',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages'
        );
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set default options if needed
        if (!get_option('ew_simple_slider_version')) {
            add_option('ew_simple_slider_version', EW_SIMPLE_SLIDER_VERSION);
        }
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
}

// Initialize the plugin
EW_Simple_Slider::get_instance();

/**
 * Helper function to get plugin instance
 */
function ew_simple_slider() {
    return EW_Simple_Slider::get_instance();
}
