/**
 * WordPress dependencies
 */
import { useBlockProps } from '@wordpress/block-editor';
import { __ } from '@wordpress/i18n';

/**
 * Save component for the EW Simple Slider block
 * Renders the HTML structure for the frontend
 */
export default function save({ attributes }) {
	const {
		slides,
		autoplay,
		autoplaySpeed,
		transitionSpeed,
		animationType,
		showArrows,
		showDots,
		pauseOnHover,
		infinite,
		slidesToShow,
		aspectRatio,
		arrowStyle,
		dotStyle,
	} = attributes;

	// Don't render if no slides
	if (!slides || slides.length === 0) {
		return null;
	}

	// Block props with data attributes for JavaScript
	const blockProps = useBlockProps.save({
		className: `ew-simple-slider ew-simple-slider--${animationType}`,
		'data-autoplay': autoplay,
		'data-autoplay-speed': autoplaySpeed,
		'data-transition-speed': transitionSpeed,
		'data-animation-type': animationType,
		'data-pause-on-hover': pauseOnHover,
		'data-infinite': infinite,
		'data-slides-to-show-mobile': slidesToShow.mobile,
		'data-slides-to-show-tablet': slidesToShow.tablet,
		'data-slides-to-show-desktop': slidesToShow.desktop,
	});

	// Extract background color classes and styles from blockProps
	const hasBackgroundColor = blockProps.className?.includes('has-') && blockProps.className?.includes('-background-color');
	const containerClasses = `ew-simple-slider__container ${hasBackgroundColor ? 'has-background' : ''}`;

	return (
		<div {...blockProps}>
			<div
				className={containerClasses}
				style={{ aspectRatio }}
				role="region"
				aria-label={__('Image slider', 'ew-simple-slider')}
			>
				{/* Slides container */}
				<div 
					className="ew-simple-slider__slides"
					role="group"
					aria-live="polite"
					aria-atomic="false"
				>
					{slides.map((slide, index) => {
						const slideContent = (
							<div className="ew-simple-slider__slide-content">
								<div className="ew-simple-slider__image-container">
									<div className="ew-simple-slider__image-placeholder">
										<div className="ew-simple-slider__loading-spinner"></div>
									</div>
									<img
										src={slide.url}
										alt={slide.alt || ''}
										className="ew-simple-slider__slide-image"
										loading="lazy"
										decoding="async"
										fetchpriority={index === 0 ? 'high' : 'low'}
										onLoad={(e) => {
											e.target.parentElement.classList.add('loaded');
										}}
									/>
								</div>
								
								{/* Text overlay */}
								{(slide.title || slide.description) && (
									<div className="ew-simple-slider__slide-overlay">
										<div className="ew-simple-slider__slide-text">
											{slide.title && (
												<h3 className="ew-simple-slider__slide-title">
													{slide.title}
												</h3>
											)}
											{slide.description && (
												<p className="ew-simple-slider__slide-description">
													{slide.description}
												</p>
											)}
										</div>
									</div>
								)}
							</div>
						);

						return (
							<div
								key={slide.id || index}
								id={`slide-${index}`}
								className={`ew-simple-slider__slide ${index === 0 ? 'is-active' : ''}`}
								role="group"
								aria-roledescription={__('slide', 'ew-simple-slider')}
								aria-label={__('Slide %1$d of %2$d', 'ew-simple-slider')
									.replace('%1$d', index + 1)
									.replace('%2$d', slides.length)}
								data-slide-index={index}
								aria-hidden={index === 0 ? 'false' : 'true'}
							>
								{slide.link ? (
									<a
										href={slide.link}
										target={slide.linkTarget || '_self'}
										rel={slide.linkTarget === '_blank' ? 'noopener noreferrer' : undefined}
										className="ew-simple-slider__slide-link"
										aria-label={slide.title || slide.alt || __('View slide content', 'ew-simple-slider')}
									>
										{slideContent}
									</a>
								) : (
									slideContent
								)}
							</div>
						);
					})}
				</div>

				{/* Navigation arrows */}
				{showArrows && slides.length > 1 && (
					<div className="ew-simple-slider__navigation">
						<button
							className={`ew-simple-slider__arrow ew-simple-slider__arrow--prev ew-simple-slider__arrow--${arrowStyle}`}
							type="button"
							aria-label={__('Previous slide', 'ew-simple-slider')}
							data-direction="prev"
						>
							<svg 
								width="24" 
								height="24" 
								viewBox="0 0 24 24" 
								fill="none" 
								xmlns="http://www.w3.org/2000/svg"
								aria-hidden="true"
							>
								<path 
									d="M15 18L9 12L15 6" 
									stroke="currentColor" 
									strokeWidth="2" 
									strokeLinecap="round" 
									strokeLinejoin="round"
								/>
							</svg>
							<span className="screen-reader-text">
								{__('Previous slide', 'ew-simple-slider')}
							</span>
						</button>

						<button
							className={`ew-simple-slider__arrow ew-simple-slider__arrow--next ew-simple-slider__arrow--${arrowStyle}`}
							type="button"
							aria-label={__('Next slide', 'ew-simple-slider')}
							data-direction="next"
						>
							<svg 
								width="24" 
								height="24" 
								viewBox="0 0 24 24" 
								fill="none" 
								xmlns="http://www.w3.org/2000/svg"
								aria-hidden="true"
							>
								<path 
									d="M9 18L15 12L9 6" 
									stroke="currentColor" 
									strokeWidth="2" 
									strokeLinecap="round" 
									strokeLinejoin="round"
								/>
							</svg>
							<span className="screen-reader-text">
								{__('Next slide', 'ew-simple-slider')}
							</span>
						</button>
					</div>
				)}

				{/* Dots navigation */}
				{showDots && slides.length > 1 && (
					<div 
						className={`ew-simple-slider__dots ew-simple-slider__dots--${dotStyle}`}
						role="tablist"
						aria-label={__('Slide navigation', 'ew-simple-slider')}
					>
						{slides.map((_, index) => (
							<button
								key={index}
								className={`ew-simple-slider__dot ${index === 0 ? 'is-active' : ''}`}
								type="button"
								role="tab"
								aria-selected={index === 0 ? 'true' : 'false'}
								aria-controls={`slide-${index}`}
								aria-label={__('Go to slide %d', 'ew-simple-slider').replace('%d', index + 1)}
								data-slide-index={index}
								tabIndex={index === 0 ? 0 : -1}
							>
								<span className="screen-reader-text">
									{__('Slide %d', 'ew-simple-slider').replace('%d', index + 1)}
								</span>
							</button>
						))}
					</div>
				)}

				{/* Autoplay controls */}
				{autoplay && (
					<div className="ew-simple-slider__autoplay-controls">
						<button
							className="ew-simple-slider__play-pause"
							type="button"
							aria-label={__('Pause slideshow', 'ew-simple-slider')}
							data-action="pause"
						>
							{/* Pause icon */}
							<svg 
								width="24" 
								height="24" 
								viewBox="0 0 24 24" 
								fill="none" 
								xmlns="http://www.w3.org/2000/svg"
								aria-hidden="true"
								className="ew-simple-slider__pause-icon"
							>
								<rect x="6" y="4" width="4" height="16" fill="currentColor"/>
								<rect x="14" y="4" width="4" height="16" fill="currentColor"/>
							</svg>
							
							{/* Play icon */}
							<svg 
								width="24" 
								height="24" 
								viewBox="0 0 24 24" 
								fill="none" 
								xmlns="http://www.w3.org/2000/svg"
								aria-hidden="true"
								className="ew-simple-slider__play-icon"
								style={{ display: 'none' }}
							>
								<polygon points="5,3 19,12 5,21" fill="currentColor"/>
							</svg>
							
							<span className="screen-reader-text">
								{__('Pause slideshow', 'ew-simple-slider')}
							</span>
						</button>
					</div>
				)}

				{/* Screen reader announcements */}
				<div 
					className="ew-simple-slider__sr-announcements" 
					aria-live="polite" 
					aria-atomic="true"
					style={{ 
						position: 'absolute',
						left: '-10000px',
						width: '1px',
						height: '1px',
						overflow: 'hidden'
					}}
				>
					{/* JavaScript will update this with slide changes */}
				</div>

				{/* Progress indicator for autoplay */}
				{autoplay && (
					<div className="ew-simple-slider__progress">
						<div className="ew-simple-slider__progress-bar"></div>
					</div>
				)}
			</div>
		</div>
	);
}
