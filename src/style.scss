/**
 * EW Simple Slider - Frontend Styles
 * Mobile-first responsive design with CSS variables and BEM methodology
 */

/* CSS Variables */
:root {
	/* Colors */
	--ew-slider-primary-color: #007cba;
	--ew-slider-secondary-color: #ffffff;
	--ew-slider-text-color: #ffffff;
	--ew-slider-background-color: #000000;
	--ew-slider-overlay-color: rgba(0, 0, 0, 0.5);
	--ew-slider-border-color: rgba(255, 255, 255, 0.2);
	
	/* Spacing */
	--ew-slider-spacing-xs: 0.25rem;
	--ew-slider-spacing-sm: 0.5rem;
	--ew-slider-spacing-md: 1rem;
	--ew-slider-spacing-lg: 1.5rem;
	--ew-slider-spacing-xl: 2rem;
	
	/* Typography */
	--ew-slider-font-size-sm: 0.875rem;
	--ew-slider-font-size-md: 1rem;
	--ew-slider-font-size-lg: 1.25rem;
	--ew-slider-font-size-xl: 1.5rem;
	--ew-slider-font-size-2xl: 2rem;
	--ew-slider-line-height: 1.5;
	
	/* Transitions */
	--ew-slider-transition-duration: 0.3s;
	--ew-slider-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
	
	/* Borders */
	--ew-slider-border-radius: 8px;
	--ew-slider-border-radius-sm: 4px;
	--ew-slider-border-radius-lg: 12px;
	
	/* Shadows */
	--ew-slider-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
	--ew-slider-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
	--ew-slider-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
	
	/* Z-index */
	--ew-slider-z-base: 1;
	--ew-slider-z-overlay: 10;
	--ew-slider-z-controls: 20;
	--ew-slider-z-modal: 1000;
}

/* Base slider styles */
.ew-simple-slider {
	position: relative;
	width: 100%;
	max-width: 100%;
	margin: 0 auto;
	
	/* Ensure proper stacking context */
	isolation: isolate;
	
	&__container {
		position: relative;
		width: 100%;
		overflow: hidden;
		border-radius: var(--ew-slider-border-radius);
		background-color: var(--ew-slider-background-color);

		/* Default aspect ratio */
		aspect-ratio: 16/9;

		/* Focus styles for keyboard navigation */
		&:focus-within {
			outline: 2px solid var(--ew-slider-primary-color);
			outline-offset: 2px;
		}

		/* Support for custom background colors from Gutenberg */
		&.has-background {
			background-color: inherit;
		}
	}
	
	&__slides {
		position: relative;
		width: 100%;
		height: 100%;
		display: flex;
		transition: transform var(--ew-slider-transition-duration) var(--ew-slider-transition-timing);
	}
	
	&__slide {
		position: relative;
		flex: 0 0 100%;
		width: 100%;
		height: 100%;
		opacity: 0;
		transition: opacity var(--ew-slider-transition-duration) var(--ew-slider-transition-timing);
		
		&.is-active {
			opacity: 1;
		}
		
		/* Slide animation variants */
		.ew-simple-slider--slide & {
			transform: translateX(100%);
			
			&.is-active {
				transform: translateX(0);
			}
			
			&.is-prev {
				transform: translateX(-100%);
			}
		}
		
		.ew-simple-slider--fade & {
			position: absolute;
			top: 0;
			left: 0;
		}
	}
	
	&__slide-content {
		position: relative;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	&__image-container {
		position: relative;
		width: 100%;
		height: 100%;
		overflow: hidden;

		&:not(.loaded) .ew-simple-slider__image-placeholder {
			opacity: 1;
		}

		&.loaded .ew-simple-slider__image-placeholder {
			opacity: 0;
		}

		&.loaded .ew-simple-slider__slide-image {
			opacity: 1;
		}
	}

	&__image-placeholder {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: var(--ew-slider-background-color, #f0f0f0);
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 1;
		transition: opacity 0.3s ease;
		z-index: 1;
	}

	&__loading-spinner {
		width: 40px;
		height: 40px;
		border: 3px solid rgba(255, 255, 255, 0.3);
		border-top: 3px solid var(--ew-slider-primary-color, #007cba);
		border-radius: 50%;
		animation: ew-slider-spin 1s linear infinite;
	}

	&__slide-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		object-fit: cover;
		object-position: center;
		display: block;
		opacity: 0;
		transition: opacity 0.3s ease;
		z-index: 2;

		/* Performance optimizations */
		will-change: auto;
		backface-visibility: hidden;
	}
	
	&__slide-link {
		display: block;
		width: 100%;
		height: 100%;
		text-decoration: none;
		color: inherit;
		
		&:focus {
			outline: 2px solid var(--ew-slider-primary-color);
			outline-offset: -2px;
		}
	}
	
	&__slide-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(
			to top,
			var(--ew-slider-overlay-color) 0%,
			transparent 100%
		);
		padding: var(--ew-slider-spacing-lg);
		z-index: var(--ew-slider-z-overlay);
	}
	
	&__slide-text {
		color: var(--ew-slider-text-color);
	}
	
	&__slide-title {
		margin: 0 0 var(--ew-slider-spacing-sm) 0;
		font-size: var(--ew-slider-font-size-xl);
		font-weight: 600;
		line-height: var(--ew-slider-line-height);
		
		/* Responsive typography */
		@media (min-width: 768px) {
			font-size: var(--ew-slider-font-size-2xl);
		}
	}
	
	&__slide-description {
		margin: 0;
		font-size: var(--ew-slider-font-size-md);
		line-height: var(--ew-slider-line-height);
		opacity: 0.9;
		
		/* Limit text length on mobile */
		@media (max-width: 767px) {
			font-size: var(--ew-slider-font-size-sm);
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			overflow: hidden;
		}
	}
}

/* Navigation arrows */
.ew-simple-slider__navigation {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	z-index: var(--ew-slider-z-controls);
}

.ew-simple-slider__arrow {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	background: var(--ew-slider-secondary-color);
	border: none;
	border-radius: var(--ew-slider-border-radius);
	width: 48px;
	height: 48px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all var(--ew-slider-transition-duration) var(--ew-slider-transition-timing);
	box-shadow: var(--ew-slider-shadow-md);
	pointer-events: auto;
	color: var(--ew-slider-background-color);
	
	/* Focus styles */
	&:focus {
		outline: 2px solid var(--ew-slider-primary-color);
		outline-offset: 2px;
	}
	
	/* Hover styles */
	&:hover {
		background: var(--ew-slider-primary-color);
		color: var(--ew-slider-secondary-color);
		transform: translateY(-50%) scale(1.05);
	}
	
	/* Active styles */
	&:active {
		transform: translateY(-50%) scale(0.95);
	}
	
	/* Disabled styles */
	&:disabled {
		opacity: 0.5;
		cursor: not-allowed;
		
		&:hover {
			background: var(--ew-slider-secondary-color);
			color: var(--ew-slider-background-color);
			transform: translateY(-50%);
		}
	}
	
	/* Arrow positioning */
	&--prev {
		left: var(--ew-slider-spacing-md);
	}
	
	&--next {
		right: var(--ew-slider-spacing-md);
	}
	
	/* Arrow style variations */
	&--minimal {
		background: rgba(255, 255, 255, 0.8);
		backdrop-filter: blur(4px);
		border-radius: 50%;
		width: 40px;
		height: 40px;
	}
	
	&--rounded {
		border-radius: var(--ew-slider-border-radius-lg);
		width: 56px;
		height: 56px;
	}
	
	/* Mobile adjustments */
	@media (max-width: 767px) {
		width: 40px;
		height: 40px;
		
		&--prev {
			left: var(--ew-slider-spacing-sm);
		}
		
		&--next {
			right: var(--ew-slider-spacing-sm);
		}
	}
}

/* Screen reader text */
.screen-reader-text {
	position: absolute !important;
	clip: rect(1px, 1px, 1px, 1px);
	clip-path: inset(50%);
	width: 1px;
	height: 1px;
	overflow: hidden;
	word-wrap: normal !important;
}

/* Dots navigation */
.ew-simple-slider__dots {
	position: absolute;
	bottom: var(--ew-slider-spacing-md);
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	gap: var(--ew-slider-spacing-sm);
	z-index: var(--ew-slider-z-controls);
	
	/* Mobile adjustments */
	@media (max-width: 767px) {
		bottom: var(--ew-slider-spacing-sm);
		gap: var(--ew-slider-spacing-xs);
	}
}

.ew-simple-slider__dot {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	border: 2px solid var(--ew-slider-secondary-color);
	background: transparent;
	cursor: pointer;
	transition: all var(--ew-slider-transition-duration) var(--ew-slider-transition-timing);
	opacity: 0.7;
	
	/* Focus styles */
	&:focus {
		outline: 2px solid var(--ew-slider-primary-color);
		outline-offset: 2px;
	}
	
	/* Hover styles */
	&:hover {
		opacity: 1;
		transform: scale(1.2);
	}
	
	/* Active state */
	&.is-active {
		background: var(--ew-slider-secondary-color);
		opacity: 1;
	}
	
	/* Dot style variations */
	.ew-simple-slider__dots--minimal & {
		width: 8px;
		height: 8px;
		border-width: 1px;
	}
	
	.ew-simple-slider__dots--square & {
		border-radius: var(--ew-slider-border-radius-sm);
		width: 16px;
		height: 4px;
	}
}

/* Autoplay controls */
.ew-simple-slider__autoplay-controls {
	position: absolute;
	top: var(--ew-slider-spacing-md);
	right: var(--ew-slider-spacing-md);
	z-index: var(--ew-slider-z-controls);
}

.ew-simple-slider__play-pause {
	background: rgba(0, 0, 0, 0.5);
	border: none;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all var(--ew-slider-transition-duration) var(--ew-slider-transition-timing);
	color: var(--ew-slider-secondary-color);
	
	&:focus {
		outline: 2px solid var(--ew-slider-primary-color);
		outline-offset: 2px;
	}
	
	&:hover {
		background: rgba(0, 0, 0, 0.7);
		transform: scale(1.05);
	}
	
	/* Icon states */
	&[data-state="playing"] .ew-simple-slider__pause-icon {
		display: block;
	}
	
	&[data-state="playing"] .ew-simple-slider__play-icon {
		display: none;
	}
	
	&[data-state="paused"] .ew-simple-slider__pause-icon {
		display: none;
	}
	
	&[data-state="paused"] .ew-simple-slider__play-icon {
		display: block;
	}
}

/* Progress indicator */
.ew-simple-slider__progress {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 4px;
	background: rgba(255, 255, 255, 0.2);
	z-index: var(--ew-slider-z-controls);
}

.ew-simple-slider__progress-bar {
	height: 100%;
	background: var(--ew-slider-primary-color);
	width: 0;
	transition: width linear;
}

/* Responsive breakpoints */
@media (min-width: 768px) {
	.ew-simple-slider {
		&__slide-overlay {
			padding: var(--ew-slider-spacing-xl);
		}
	}
}

@media (min-width: 1024px) {
	.ew-simple-slider {
		&__container {
			border-radius: var(--ew-slider-border-radius-lg);
		}
	}
}

/* High contrast mode support */
@media (prefers-contrast: high) {
	.ew-simple-slider {
		&__arrow {
			border: 2px solid;
		}

		&__dot {
			border-width: 3px;
		}

		&__slide-overlay {
			background: rgba(0, 0, 0, 0.8);
		}
	}
}

/* Keyframe animations */
@keyframes ew-slider-spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
	.ew-simple-slider {
		&__slides,
		&__slide,
		&__arrow,
		&__dot,
		&__play-pause,
		&__progress-bar,
		&__loading-spinner {
			transition: none !important;
			animation: none !important;
		}

		/* Disable autoplay for users who prefer reduced motion */
		&[data-autoplay="true"] {
			--ew-slider-autoplay-disabled: 1;
		}
	}
}
