<?php
/**
 * EW Simple Slider - Debug Check
 * Archivo para verificar que el plugin se está registrando correctamente
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Debug function to check plugin status
 */
function ew_simple_slider_debug_check() {
    echo '<div class="notice notice-info"><p><strong>EW Simple Slider Debug Check:</strong></p>';
    
    // Check if plugin is active
    if (class_exists('EW_Simple_Slider')) {
        echo '<p>✅ Plugin class exists and is loaded</p>';
    } else {
        echo '<p>❌ Plugin class not found</p>';
    }
    
    // Check if block is registered
    if (WP_Block_Type_Registry::get_instance()->is_registered('ew-simple-slider/slider')) {
        echo '<p>✅ Block is registered in WordPress</p>';
    } else {
        echo '<p>❌ Block is NOT registered</p>';
    }
    
    // Check if files exist
    $plugin_dir = plugin_dir_path(__FILE__);
    $files_to_check = [
        'block.json',
        'build/index.js',
        'build/index.css',
        'build/style-index.css',
        'build/script.js'
    ];
    
    echo '<p><strong>File Check:</strong></p>';
    foreach ($files_to_check as $file) {
        $file_path = $plugin_dir . $file;
        if (file_exists($file_path)) {
            echo '<p>✅ ' . $file . ' exists</p>';
        } else {
            echo '<p>❌ ' . $file . ' missing</p>';
        }
    }
    
    // Check WordPress version
    global $wp_version;
    echo '<p><strong>WordPress Version:</strong> ' . $wp_version . '</p>';
    
    // Check PHP version
    echo '<p><strong>PHP Version:</strong> ' . PHP_VERSION . '</p>';
    
    // Check if Gutenberg is active
    if (function_exists('register_block_type')) {
        echo '<p>✅ Block editor (Gutenberg) is available</p>';
    } else {
        echo '<p>❌ Block editor not available</p>';
    }
    
    // Check registered blocks
    $registered_blocks = WP_Block_Type_Registry::get_instance()->get_all_registered();
    $ew_blocks = array_filter(array_keys($registered_blocks), function($block_name) {
        return strpos($block_name, 'ew-simple-slider') !== false;
    });
    
    if (!empty($ew_blocks)) {
        echo '<p><strong>EW Slider blocks found:</strong></p>';
        foreach ($ew_blocks as $block_name) {
            echo '<p>• ' . $block_name . '</p>';
        }
    } else {
        echo '<p>❌ No EW Slider blocks found in registry</p>';
    }
    
    echo '</div>';
}

// Add admin notice to show debug info
add_action('admin_notices', 'ew_simple_slider_debug_check');

/**
 * Add debug menu item
 */
function ew_simple_slider_debug_menu() {
    add_submenu_page(
        'tools.php',
        'EW Slider Debug',
        'EW Slider Debug',
        'manage_options',
        'ew-slider-debug',
        'ew_simple_slider_debug_page'
    );
}
add_action('admin_menu', 'ew_simple_slider_debug_menu');

/**
 * Debug page content
 */
function ew_simple_slider_debug_page() {
    ?>
    <div class="wrap">
        <h1>EW Simple Slider - Debug Information</h1>
        
        <div class="card">
            <h2>Plugin Status</h2>
            <?php
            // Check plugin status
            if (class_exists('EW_Simple_Slider')) {
                echo '<p style="color: green;">✅ Plugin is loaded and active</p>';
                
                // Get plugin instance
                $instance = EW_Simple_Slider::get_instance();
                echo '<p>Plugin instance created successfully</p>';
            } else {
                echo '<p style="color: red;">❌ Plugin class not found</p>';
            }
            ?>
        </div>
        
        <div class="card">
            <h2>Block Registration</h2>
            <?php
            $registry = WP_Block_Type_Registry::get_instance();
            if ($registry->is_registered('ew-simple-slider/slider')) {
                echo '<p style="color: green;">✅ Block "ew-simple-slider/slider" is registered</p>';
                
                $block = $registry->get_registered('ew-simple-slider/slider');
                echo '<p><strong>Block details:</strong></p>';
                echo '<pre>' . print_r($block, true) . '</pre>';
            } else {
                echo '<p style="color: red;">❌ Block is NOT registered</p>';
                
                echo '<p><strong>All registered blocks:</strong></p>';
                $all_blocks = array_keys($registry->get_all_registered());
                sort($all_blocks);
                echo '<pre>' . implode("\n", $all_blocks) . '</pre>';
            }
            ?>
        </div>
        
        <div class="card">
            <h2>File System Check</h2>
            <?php
            $plugin_dir = plugin_dir_path(__FILE__);
            echo '<p><strong>Plugin directory:</strong> ' . $plugin_dir . '</p>';
            
            $files = [
                'ew-simple-slider.php' => 'Main plugin file',
                'block.json' => 'Block metadata',
                'build/index.js' => 'Editor JavaScript',
                'build/index.css' => 'Editor CSS',
                'build/style-index.css' => 'Frontend CSS',
                'build/script.js' => 'Frontend JavaScript'
            ];
            
            foreach ($files as $file => $description) {
                $path = $plugin_dir . $file;
                if (file_exists($path)) {
                    echo '<p style="color: green;">✅ ' . $description . ' (' . $file . ')</p>';
                } else {
                    echo '<p style="color: red;">❌ ' . $description . ' (' . $file . ') - MISSING</p>';
                }
            }
            ?>
        </div>
        
        <div class="card">
            <h2>System Information</h2>
            <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
            <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
            <p><strong>Block Editor Available:</strong> <?php echo function_exists('register_block_type') ? 'Yes' : 'No'; ?></p>
            <p><strong>Current Theme:</strong> <?php echo wp_get_theme()->get('Name'); ?></p>
        </div>
        
        <div class="card">
            <h2>Quick Fix Actions</h2>
            <p>If the block is not showing up, try these steps:</p>
            <ol>
                <li>Deactivate and reactivate the plugin</li>
                <li>Clear any caching plugins</li>
                <li>Check that WordPress is version 5.8 or higher</li>
                <li>Verify that the block editor is enabled</li>
                <li>Check browser console for JavaScript errors</li>
            </ol>
        </div>
    </div>
    <?php
}
?>
